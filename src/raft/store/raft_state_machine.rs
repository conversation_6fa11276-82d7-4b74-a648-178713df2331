//! RaftStateMachine trait implementation for Store
//! 
//! This module provides the state machine functionality required by openraft.
//! The RaftStateMachine trait is responsible for applying committed log entries
//! to the application state and managing snapshots.

use crate::error::Result;
use crate::raft::types::*;
use super::types::Store;
use openraft::{
    storage::{RaftStateMachine, Snapshot, SnapshotMeta},
    StorageError, ErrorSubject, ErrorVerb,
    LogId, Entry, EntryPayload,
};
use std::io::Cursor;
use tracing::{debug, error, info, warn};

/// Snapshot metadata for storage
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ConfluxSnapshotMeta {
    /// Last included log ID
    pub last_log_id: Option<LogId<NodeId>>,
    /// Membership at the time of snapshot
    pub membership: Option<openraft::Membership<NodeId, Node>>,
    /// Snapshot creation timestamp
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// Snapshot size in bytes
    pub size_bytes: u64,
}

impl Store {
    /// Apply a single command to the state machine
    async fn apply_single_command(&self, command: &RaftCommand) -> Result<ClientWriteResponse> {
        debug!("Applying command to state machine: {:?}", command);
        
        // Use the existing apply_command method
        self.apply_command(command).await
    }

    /// Create a snapshot of the current state
    pub async fn create_snapshot(&self) -> Result<(ConfluxSnapshotMeta, Vec<u8>)> {
        info!("Creating state machine snapshot");
        
        // TODO: Implement actual snapshot creation
        // For now, create a minimal snapshot
        let snapshot_data = serde_json::json!({
            "configs_count": self.get_storage_stats().await?.configs_count,
            "versions_count": self.get_storage_stats().await?.versions_count,
            "snapshot_version": "1.0.0",
            "created_at": chrono::Utc::now(),
        });
        
        let data = serde_json::to_vec(&snapshot_data).map_err(|e| {
            crate::error::ConfluxError::storage(format!("Failed to serialize snapshot: {}", e))
        })?;
        
        let meta = ConfluxSnapshotMeta {
            last_log_id: None, // TODO: Get actual last log ID
            membership: None,   // TODO: Get current membership
            created_at: chrono::Utc::now(),
            size_bytes: data.len() as u64,
        };
        
        info!("Created snapshot with {} bytes", data.len());
        Ok((meta, data))
    }

    /// Install a snapshot, replacing the current state
    pub async fn install_snapshot_data(&self, meta: &ConfluxSnapshotMeta, data: &[u8]) -> Result<()> {
        info!("Installing snapshot with {} bytes", data.len());
        
        // TODO: Implement actual snapshot installation
        // This would involve:
        // 1. Parsing the snapshot data
        // 2. Clearing current state
        // 3. Restoring state from snapshot
        
        let snapshot_data: serde_json::Value = serde_json::from_slice(data).map_err(|e| {
            crate::error::ConfluxError::storage(format!("Failed to deserialize snapshot: {}", e))
        })?;
        
        debug!("Snapshot data: {:?}", snapshot_data);
        
        info!("Snapshot installed successfully (placeholder)");
        Ok(())
    }
}

#[async_trait::async_trait]
impl RaftStateMachine<TypeConfig> for Store {
    type SnapshotBuilder = Self;

    async fn applied_state(&mut self) -> Result<(Option<LogId<NodeId>>, openraft::membership::StoredMembership<NodeId, Node>), StorageError<NodeId>> {
        debug!("Getting applied state");
        
        // TODO: Implement actual applied state tracking
        // For now, return empty state
        Ok((
            None, // last_applied_log_id
            openraft::membership::StoredMembership::default(),
        ))
    }

    async fn apply<I>(&mut self, entries: I) -> Result<Vec<ClientWriteResponse>, StorageError<NodeId>>
    where
        I: IntoIterator<Item = Entry<TypeConfig>> + Send,
    {
        debug!("Applying entries to state machine");
        
        let mut responses = Vec::new();
        
        for entry in entries {
            match entry.payload {
                EntryPayload::Normal(ref client_request) => {
                    // Apply the command to the state machine
                    match self.apply_single_command(&client_request.command).await {
                        Ok(response) => {
                            responses.push(response);
                        }
                        Err(e) => {
                            error!("Failed to apply command: {}", e);
                            responses.push(ClientWriteResponse {
                                success: false,
                                message: format!("Failed to apply command: {}", e),
                                data: None,
                            });
                        }
                    }
                }
                EntryPayload::Membership(ref membership) => {
                    // Handle membership changes
                    info!("Applying membership change: {:?}", membership);
                    
                    // TODO: Update local membership state
                    responses.push(ClientWriteResponse {
                        success: true,
                        message: "Membership updated".to_string(),
                        data: Some(serde_json::json!({
                            "membership": membership.get_joint_config(),
                        })),
                    });
                }
                EntryPayload::Blank => {
                    // Blank entry for leader confirmation
                    debug!("Applying blank entry");
                    responses.push(ClientWriteResponse {
                        success: true,
                        message: "Blank entry applied".to_string(),
                        data: None,
                    });
                }
            }
        }
        
        info!("Applied {} entries, generated {} responses", responses.len(), responses.len());
        Ok(responses)
    }

    async fn get_snapshot_builder(&mut self) -> Self::SnapshotBuilder {
        // Return self as the snapshot builder
        self.clone()
    }

    async fn begin_receiving_snapshot(&mut self) -> Result<Box<Cursor<Vec<u8>>>, StorageError<NodeId>> {
        debug!("Beginning to receive snapshot");
        
        // Create a cursor for writing snapshot data
        let cursor = Box::new(Cursor::new(Vec::new()));
        Ok(cursor)
    }

    async fn install_snapshot(
        &mut self,
        meta: &SnapshotMeta<NodeId, Node>,
        snapshot: Box<Cursor<Vec<u8>>>,
    ) -> Result<(), StorageError<NodeId>> {
        info!("Installing snapshot: {:?}", meta);
        
        let data = snapshot.into_inner();
        
        // Create our own meta from the openraft meta
        let our_meta = ConfluxSnapshotMeta {
            last_log_id: meta.last_log_id.clone(),
            membership: Some(meta.membership.clone()),
            created_at: chrono::Utc::now(),
            size_bytes: data.len() as u64,
        };
        
        // Install the snapshot
        if let Err(e) = self.install_snapshot_data(&our_meta, &data).await {
            error!("Failed to install snapshot: {}", e);
            return Err(StorageError::new(
                ErrorSubject::StateMachine,
                ErrorVerb::Write,
                crate::error::ConfluxError::storage(format!("Failed to install snapshot: {}", e)).into(),
            ));
        }
        
        info!("Snapshot installed successfully");
        Ok(())
    }

    async fn get_current_snapshot(&mut self) -> Result<Option<Snapshot<TypeConfig>>, StorageError<NodeId>> {
        debug!("Getting current snapshot");
        
        // TODO: Implement actual snapshot retrieval
        // For now, return None to indicate no snapshot available
        Ok(None)
    }
}

#[async_trait::async_trait]
impl openraft::storage::RaftSnapshotBuilder<TypeConfig> for Store {
    async fn build_snapshot(&mut self) -> Result<Snapshot<TypeConfig>, StorageError<NodeId>> {
        info!("Building snapshot");
        
        // Create the snapshot
        let (meta, data) = match self.create_snapshot().await {
            Ok((meta, data)) => (meta, data),
            Err(e) => {
                error!("Failed to create snapshot: {}", e);
                return Err(StorageError::new(
                    ErrorSubject::StateMachine,
                    ErrorVerb::Read,
                    crate::error::ConfluxError::storage(format!("Failed to create snapshot: {}", e)).into(),
                ));
            }
        };
        
        // Convert to openraft snapshot format
        let snapshot_meta = SnapshotMeta {
            last_log_id: meta.last_log_id,
            last_membership: meta.membership.unwrap_or_default(),
            snapshot_id: format!("snapshot-{}", meta.created_at.timestamp()),
        };
        
        let snapshot = Snapshot {
            meta: snapshot_meta,
            snapshot: Box::new(Cursor::new(data)),
        };
        
        info!("Snapshot built successfully");
        Ok(snapshot)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::collections::BTreeMap;

    #[tokio::test]
    async fn test_applied_state() {
        let temp_dir = TempDir::new().unwrap();
        let mut store = Store::new(temp_dir.path()).await.unwrap();
        
        let (log_id, membership) = store.applied_state().await.unwrap();
        assert!(log_id.is_none());
    }

    #[tokio::test]
    async fn test_apply_entries() {
        let temp_dir = TempDir::new().unwrap();
        let mut store = Store::new(temp_dir.path()).await.unwrap();
        
        // Create a test entry
        let namespace = ConfigNamespace {
            tenant: "test".to_string(),
            app: "app".to_string(),
            env: "dev".to_string(),
        };
        
        let command = RaftCommand::CreateConfig {
            namespace,
            name: "test-config".to_string(),
            content: b"test content".to_vec(),
            format: ConfigFormat::Json,
            schema: None,
            creator_id: 1,
            description: "Test config".to_string(),
        };
        
        let client_request = ClientRequest { command };
        
        let entry = Entry {
            log_id: Some(LogId::new(0, 1, 1)),
            payload: EntryPayload::Normal(client_request),
        };
        
        let responses = store.apply(vec![entry]).await.unwrap();
        assert_eq!(responses.len(), 1);
        assert!(responses[0].success);
    }

    #[tokio::test]
    async fn test_snapshot_creation() {
        let temp_dir = TempDir::new().unwrap();
        let store = Store::new(temp_dir.path()).await.unwrap();
        
        let (meta, data) = store.create_snapshot().await.unwrap();
        assert!(data.len() > 0);
        assert!(meta.size_bytes > 0);
    }

    #[tokio::test]
    async fn test_snapshot_builder() {
        let temp_dir = TempDir::new().unwrap();
        let mut store = Store::new(temp_dir.path()).await.unwrap();
        
        let mut builder = store.get_snapshot_builder().await;
        let snapshot = builder.build_snapshot().await.unwrap();
        assert!(!snapshot.meta.snapshot_id.is_empty());
    }
}
